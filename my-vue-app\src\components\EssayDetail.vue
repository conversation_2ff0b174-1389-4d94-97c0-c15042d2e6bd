<template>
  <div class="essay-detail">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-container">
          <img :src="signalIcon" alt="信号" class="signal-icon" />
          <img :src="signalBar1" alt="信号条1" class="signal-bar" />
          <img :src="signalBar2" alt="信号条2" class="signal-bar" />
          <img :src="signalBar3" alt="信号条3" class="signal-bar" />
        </div>
      </div>
      <div class="status-right">
        <div class="battery-container">
          <img :src="batteryOutline" alt="电池外框" class="battery-outline" />
          <img :src="batteryFill2" alt="电池电量" class="battery-fill" />
        </div>
      </div>
    </div>

    <!-- 头部导航 -->
    <div class="header">
      <div class="back-button" @click="goBack">
        <img :src="arrowLeftFill" alt="返回" />
      </div>
      <div class="header-title">详情</div>
    </div>

    <!-- 标签页导航 -->
    <div class="tab-navigation">
      <div class="tab-item" :class="{ active: currentTab === 'review' }" @click="switchTab('review')">
        <span>作文点评</span>
        <div class="tab-indicator" v-if="currentTab === 'review'"></div>
      </div>
      <div class="tab-item" :class="{ active: currentTab === 'report' }" @click="switchTab('report')">
        <span>作文报告</span>
        <div class="tab-indicator" v-if="currentTab === 'report'"></div>
      </div>
      <div class="tab-item" :class="{ active: currentTab === 'sample' }" @click="switchTab('sample')">
        <span>润色范文</span>
        <div class="tab-indicator" v-if="currentTab === 'sample'"></div>
      </div>
      <div class="tab-item" :class="{ active: currentTab === 'requirements' }" @click="switchTab('requirements')">
        <span>作文要求</span>
        <div class="tab-indicator" v-if="currentTab === 'requirements'"></div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 作文点评内容 -->
      <div v-if="currentTab === 'review'" class="review-content">
        <!-- 作文信息卡片 -->
        <div class="essay-info-card" @click="showEssayDetailModal">
          <div class="essay-tags">
            <span class="tag">第一单元</span>
            <span class="tag">单元作文</span>
            <span class="tag">全命题</span>
          </div>
          <div class="essay-title">我的植物朋友</div>
          <div class="essay-score">
            <span class="score-number">28</span>
            <span class="score-unit">分</span>
          </div>
        </div>

        <!-- 点评标题 -->
        <div class="section-title">点评</div>

      <!-- 思想与中心 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">思想与中心</span>
          <div class="star-rating">
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
          </div>
        </div>
        <div class="review-label">评语</div>
        <div class="review-text">文章紧扣'未来的城市生活'主题，通过多个方面描绘了未来城市的科技发展和生活变化，中心思想明确。</div>
      </div>

      <!-- 内容 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">内容</span>
          <div class="star-rating">
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
          </div>
        </div>
        <div class="review-label">评语</div>
        <div class="review-text">内容具体详细，涵盖了建筑、交通、环保、教育和医疗等多个领域，展现了未来城市生活的全面图景。</div>
      </div>

      <!-- 结构 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">结构</span>
          <div class="star-rating">
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
          </div>
        </div>
        <div class="review-label">评语</div>
        <div class="review-text">文章结构较为清晰，但部分段落之间的过渡不够自然，详略处理有待加强。</div>
      </div>

      <!-- 语言 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">语言</span>
          <div class="star-rating">
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
            <img :src="starFilled" alt="星星" class="star" />
          </div>
        </div>
        <div class="review-label">评语</div>
        <div class="review-text">语句通顺</div>
      </div>

      <!-- 总结评语 -->
      <div class="review-section">
        <div class="section-name">总结评语</div>
        <div class="review-text summary">本文围绕'未来的城市生活'这一主题，从建筑、交通、环保、教育和医疗等多个方面展开，描绘了一个科技高度发达的未来城市图景。文章内容具体，涵盖了多个领域，展现了未来城市生活的全面变化。结构上，文章层次较为清晰，但部分段落之间的过渡不够自然，详略处理有待加强。语言方面，文章语句基本通顺，但存在个别错别字和标点符号使用不当的问题，修辞手法的运用也较为有限。总体而言，文章紧扣主题，中心思想明确，内容具体，结构较为清晰，语言基本流畅，但仍有提升空间。</div>
      </div>

      <!-- 字数不足 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">字数不足</span>
          <span class="deduction">扣2分</span>
        </div>
        <div class="word-count">
          <span class="label">总字数</span>
          <span class="count">100字</span>
        </div>
      </div>

      <!-- 不足之处标题 -->
      <div class="section-title">不足之处</div>

      <!-- 错别字 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">错别字</span>
          <span class="count-info">共10处</span>
        </div>
        <div class="error-list">
          <div class="error-row">
            <span class="error-item">1.棋中（其中）</span>
            <span class="error-item">2.坐号（座号）</span>
            <span class="error-item">3.坐号（座号）</span>
            <span class="error-item">4.坐号（座号）</span>
          </div>
          <div class="error-row">
            <span class="error-item">5.棋中（其中）</span>
            <span class="error-item">6.坐号（座号）</span>
            <span class="error-item">7.坐号（座号）</span>
            <span class="error-item">8.坐号（座号）</span>
          </div>
          <div class="error-row">
            <span class="error-item">9.棋中（其中）</span>
            <span class="error-item">10.坐号（座号）</span>
          </div>
        </div>
      </div>

      <!-- 滥用拼音 -->
      <div class="review-section">
        <div class="section-header">
          <span class="section-name">滥用拼音</span>
          <span class="count-info">共10处</span>
        </div>
        <div class="error-list">
          <div class="error-row">
            <span class="error-item">1.qi（其）</span>
            <span class="error-item">2.zuo（座）</span>
            <span class="error-item">3.zuo（座）</span>
            <span class="error-item">4.zuo（座）</span>
          </div>
          <div class="error-row">
            <span class="error-item">5.zuo（座）</span>
            <span class="error-item">6.zuo（座）</span>
            <span class="error-item">7.zuo（座）</span>
            <span class="error-item">8.zuo（座）</span>
          </div>
          <div class="error-row">
            <span class="error-item">9.zuo（座）</span>
            <span class="error-item">10.zuo（座）</span>
          </div>
        </div>
      </div>

        <!-- 最后批改时间 -->
        <div class="review-time">最后批改时间：2025.07.16  15:46</div>
      </div>

      <!-- 作文报告内容 -->
      <div v-if="currentTab === 'report'" class="report-content">
        <div class="report-main-area">
          <img :src="reportBackground1" alt="" class="report-bg-line" />
          <img :src="reportBackground2" alt="" class="report-bg-line" />
        </div>
      </div>

      <!-- 作文要求内容 -->
      <div v-if="currentTab === 'requirements'" class="requirements-content">
        <!-- 标签信息区域 -->
        <div class="requirements-header">
          <span class="req-tag">第一单元</span>
          <span class="req-tag">单元作文</span>
          <span class="req-tag">全命题</span>
        </div>

        <!-- 作文信息区域 -->
        <div class="essay-info-section">
          <div class="info-row">
            <span class="info-label">作文命题</span>
            <span class="info-value">我的植物朋友</span>
          </div>
          <div class="info-row">
            <span class="info-label">字数要求</span>
            <span class="info-value">300字</span>
          </div>
          <div class="info-row">
            <span class="info-label">总分</span>
            <span class="info-value">30分</span>
          </div>
          <div class="info-row requirements-row">
            <span class="info-label">作文要求</span>
            <span class="info-value requirements-text">选择一项自己做过的小实验（可以是科学课上的，也可以是自己在家尝试的），按照"实验准备—实验过程—实验结果"的顺序写下来。重点把实验过程写清楚，可以用上"先……接着……然后……最后……"等表示顺序的词语。</span>
          </div>
        </div>

        <!-- 分隔区域 -->
        <div class="requirements-separator"></div>

        <!-- 上传作文区域 -->
        <div class="upload-section">
          <div class="upload-header-container">
            <span class="upload-title">上传作文</span>
            <img :src="warningIcon" alt="警告" class="warning-icon" />
          </div>
          <div class="upload-grid">
            <div class="upload-box">
              <img :src="uploadIcon1" alt="上传" class="upload-icon" />
              <img :src="uploadIcon2" alt="上传" class="upload-icon" />
            </div>
            <div class="upload-box">
              <img :src="uploadIcon1" alt="上传" class="upload-icon" />
              <img :src="uploadIcon2" alt="上传" class="upload-icon" />
            </div>
            <div class="upload-box">
              <img :src="uploadIcon1" alt="上传" class="upload-icon" />
              <img :src="uploadIcon2" alt="上传" class="upload-icon" />
            </div>
            <div class="upload-box">
              <img :src="uploadIcon1" alt="上传" class="upload-icon" />
              <img :src="uploadIcon2" alt="上传" class="upload-icon" />
            </div>
          </div>
        </div>
      </div>

      <!-- 润色范文内容 -->
      <div v-if="currentTab === 'sample'" class="sample-content">
        <!-- 作文标题区域 -->
        <div class="sample-title-section">
          <div class="sample-title">我的植物朋友</div>
        </div>

        <!-- 润色范文文本内容 -->
        <div class="sample-text-content">
          <p>我家窗台有位特别的朋友 —— 绿萝。它不像玫瑰那样娇气，也没有茉莉的香味，却用满满的绿色陪我长大。</p>
          <p>绿萝的叶子像一颗颗小爱心，边缘滑溜溜的，摸起来像涂了蜡。最神奇的是它的茎，细细长长地垂下来，有的能碰到窗台底下的花盆。妈妈说这叫 "吊兰"，可我觉得更像绿色的帘子。</p>
          <p>每天放学，我都会给它浇水。有一次我忘了，叶子蔫得像打了败仗的士兵。我赶紧端来清水，第二天它居然又挺直了腰板，叶片上还挂着亮晶晶的水珠，好像在对我笑。</p>
          <p>现在，绿萝的 "小帘子" 越来越长了。我常常趴在窗台上数它的叶子，看着阳光透过叶片，在墙上投下晃动的光斑。这就是我的植物朋友，安静又顽强，陪着我一天天长大。</p>
        </div>

        <!-- 底部按钮区域 -->
        <div class="sample-buttons">
          <button class="copy-button" @click="copyText">复制全文</button>
          <button class="download-button" @click="downloadText">下载</button>
        </div>
      </div>
    </div>

    <!-- 作文详情弹窗 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <!-- 弹窗头部 -->
        <div class="modal-header">
          <div class="modal-header-icons">
            <img :src="essayDetailIcon1" alt="图标1" class="header-icon" />
            <img :src="essayDetailIcon2" alt="图标2" class="header-icon" />
          </div>
          <div class="modal-back-button" @click="closeModal">
            <img :src="arrowLeftModal" alt="返回" />
          </div>
          <div class="modal-title">详情</div>
          <div class="modal-description">上传的作文</div>
          <div class="modal-subtitle">且带有批改标记</div>
          <div class="modal-note">通过弹窗的部分功能点击事件，自动定位</div>
        </div>

        <!-- 弹窗内容区域 -->
        <div class="modal-body">
          <div class="essay-content-container">
            <img :src="essayContentBg" alt="作文内容背景" class="essay-bg" />
          </div>
        </div>

        <!-- 弹窗底部标签页 -->
        <div class="modal-tabs">
          <div class="modal-tab-group">
            <div class="modal-tab" :class="{ active: modalActiveTab === 'comprehensive' }" @click="switchModalTab('comprehensive')">
              <span>综合点评</span>
            </div>
            <div class="modal-tab" :class="{ active: modalActiveTab === 'goodWords' }" @click="switchModalTab('goodWords')">
              <div class="tab-background" v-if="modalActiveTab === 'goodWords'"></div>
              <span>好词好句</span>
            </div>
            <div class="modal-tab" :class="{ active: modalActiveTab === 'polished' }" @click="switchModalTab('polished')">
              <span>润色范文</span>
            </div>
          </div>
        </div>

        <!-- 第二组标签页 -->
        <div class="modal-tabs-second">
          <div class="modal-tab-group">
            <div class="modal-tab" :class="{ active: modalActiveTab2 === 'comprehensive' }" @click="switchModalTab2('comprehensive')">
              <span>综合点评</span>
            </div>
            <div class="modal-tab" :class="{ active: modalActiveTab2 === 'sentenceReview' }" @click="switchModalTab2('sentenceReview')">
              <div class="tab-background" v-if="modalActiveTab2 === 'sentenceReview'"></div>
              <span>分句点评</span>
            </div>
            <div class="modal-tab" :class="{ active: modalActiveTab2 === 'polished' }" @click="switchModalTab2('polished')">
              <span>润色范文</span>
            </div>
          </div>
        </div>

        <!-- 数据回显说明 -->
        <div class="modal-data-note">根据批改要求及点评要求进行相关数据回显</div>
      </div>
    </div>
  </div>
</template>

<script>
import arrowLeftDetail from '../assets/images/arrow-left.svg'
import arrowLeftFill from '../assets/images/arrow-left-fill.svg'
import starFilled from '../assets/images/star-filled.svg'
import reportBackground1 from '../assets/images/report-background-1.svg'
import reportBackground2 from '../assets/images/report-background-2.svg'
import signalIcon from '../assets/images/signal-icon.svg'
import signalBar1 from '../assets/images/signal-bar1.svg'
import signalBar2 from '../assets/images/signal-bar2.svg'
import signalBar3 from '../assets/images/signal-bar3.svg'
import batteryOutline from '../assets/images/battery-outline.svg'
import batteryFill2 from '../assets/images/battery-fill2.svg'
import uploadIcon1 from '../assets/images/upload-icon1.svg'
import uploadIcon2 from '../assets/images/upload-icon2.svg'
import warningIcon from '../assets/images/warning-icon.svg'
import essayDetailIcon1 from '../assets/images/essay-detail-icon1.svg'
import essayDetailIcon2 from '../assets/images/essay-detail-icon2.svg'
import arrowLeftModal from '../assets/images/arrow-left.svg'
import essayContentBg from '../assets/images/essay-content-bg.svg'

export default {
  name: 'EssayDetail',
  emits: ['go-back'],
  data() {
    return {
      arrowLeftDetail,
      arrowLeftFill,
      starFilled,
      reportBackground1,
      reportBackground2,
      signalIcon,
      signalBar1,
      signalBar2,
      signalBar3,
      batteryOutline,
      batteryFill2,
      uploadIcon1,
      uploadIcon2,
      warningIcon,
      essayDetailIcon1,
      essayDetailIcon2,
      arrowLeftModal,
      essayContentBg,
      currentTab: 'requirements',
      showModal: false,
      modalActiveTab: 'goodWords',
      modalActiveTab2: 'sentenceReview'
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    switchTab(tab) {
      this.currentTab = tab
      console.log('切换到标签页:', tab)
    },
    copyText() {
      const text = `我家窗台有位特别的朋友 —— 绿萝。它不像玫瑰那样娇气，也没有茉莉的香味，却用满满的绿色陪我长大。

绿萝的叶子像一颗颗小爱心，边缘滑溜溜的，摸起来像涂了蜡。最神奇的是它的茎，细细长长地垂下来，有的能碰到窗台底下的花盆。妈妈说这叫 "吊兰"，可我觉得更像绿色的帘子。

每天放学，我都会给它浇水。有一次我忘了，叶子蔫得像打了败仗的士兵。我赶紧端来清水，第二天它居然又挺直了腰板，叶片上还挂着亮晶晶的水珠，好像在对我笑。

现在，绿萝的 "小帘子" 越来越长了。我常常趴在窗台上数它的叶子，看着阳光透过叶片，在墙上投下晃动的光斑。这就是我的植物朋友，安静又顽强，陪着我一天天长大。`

      navigator.clipboard.writeText(text).then(() => {
        alert('文本已复制到剪贴板')
      }).catch(err => {
        console.error('复制失败:', err)
        alert('复制失败，请手动复制')
      })
    },
    downloadText() {
      const text = `我的植物朋友

我家窗台有位特别的朋友 —— 绿萝。它不像玫瑰那样娇气，也没有茉莉的香味，却用满满的绿色陪我长大。

绿萝的叶子像一颗颗小爱心，边缘滑溜溜的，摸起来像涂了蜡。最神奇的是它的茎，细细长长地垂下来，有的能碰到窗台底下的花盆。妈妈说这叫 "吊兰"，可我觉得更像绿色的帘子。

每天放学，我都会给它浇水。有一次我忘了，叶子蔫得像打了败仗的士兵。我赶紧端来清水，第二天它居然又挺直了腰板，叶片上还挂着亮晶晶的水珠，好像在对我笑。

现在，绿萝的 "小帘子" 越来越长了。我常常趴在窗台上数它的叶子，看着阳光透过叶片，在墙上投下晃动的光斑。这就是我的植物朋友，安静又顽强，陪着我一天天长大。`

      const blob = new Blob([text], { type: 'text/plain;charset=utf-8' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '我的植物朋友.txt'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    },
    showEssayDetailModal() {
      this.showModal = true
      document.body.style.overflow = 'hidden'
    },
    closeModal() {
      this.showModal = false
      document.body.style.overflow = 'auto'
    },
    switchModalTab(tab) {
      this.modalActiveTab = tab
    },
    switchModalTab2(tab) {
      this.modalActiveTab2 = tab
    }
  }
}
</script>

<style scoped>
.essay-detail {
  width: 375px;
  height: 1426px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏样式 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
  padding-left: 18px;
}

.signal-container {
  display: flex;
  align-items: flex-end;
  gap: 1px;
  width: 27.34px;
  height: 10.7px;
  position: relative;
}

.signal-icon {
  position: absolute;
  left: 0;
  bottom: 0;
}

.signal-bar {
  position: absolute;
  bottom: 0;
}

.signal-bar:nth-child(2) { left: 9.59px; }
.signal-bar:nth-child(3) { left: 13.53px; }
.signal-bar:nth-child(4) { left: 22.86px; }

.status-right {
  display: flex;
  align-items: center;
  padding-right: 12px;
}

.battery-container {
  width: 65.87px;
  height: 10.56px;
  position: relative;
}

.battery-outline {
  position: absolute;
  left: 43.05px;
  top: 0;
  opacity: 0.35;
}

.battery-fill {
  position: absolute;
  left: 44.26px;
  top: 1.2px;
}

/* 头部导航 */
.header {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  position: relative;
}

.back-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.back-button img {
  width: 100%;
  height: 100%;
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  font-weight: 400;
}

/* 标签页导航 */
.tab-navigation {
  height: 40px;
  display: flex;
  padding: 0 23px;
  background: transparent;
}

.tab-item {
  width: 60px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.tab-item span {
  font-size: 11px;
  line-height: 18px;
  color: #565E6C;
  font-weight: 400;
}

.tab-item.active span {
  color: #636AE8;
  font-weight: 700;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 60px;
  height: 4px;
  background: #636AE8;
}

/* 作文信息卡片 */
.essay-info-card {
  height: 65px;
  background: #F8F9FA;
  padding: 11px 17px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.essay-info-card:hover {
  background: #F0F1F2;
}

.essay-tags {
  display: flex;
  gap: 12px;
}

.tag {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.essay-title {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.essay-score {
  position: absolute;
  top: 15px;
  right: 17px;
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.score-number {
  font-size: 24px;
  line-height: 36px;
  color: #DE3B40;
  font-weight: 400;
}

.score-unit {
  font-size: 12px;
  line-height: 20px;
  color: #DE3B40;
  font-weight: 400;
}

/* 内容区域 */
.content-area {
  background: #FFFFFF;
}

/* 点评内容 */
.review-content {
  padding: 0;
  background: #FFFFFF;
}

.section-title {
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
  font-weight: 400;
  padding: 10px 17px;
  margin-bottom: 10px;
}

.review-section {
  background: #F8F9FA;
  padding: 11px 17px;
  margin-bottom: 10px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.section-name {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.star-rating {
  display: flex;
  gap: 0;
}

.star {
  width: 18px;
  height: 18px;
}

.review-label {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  margin-bottom: 8px;
}

.review-text {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.review-text.summary {
  margin-top: 0;
}

.deduction {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.word-count {
  display: flex;
  gap: 19px;
  margin-top: 8px;
}

.word-count .label,
.word-count .count {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.count-info {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
}

.error-list {
  margin-top: 8px;
}

.error-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.error-row:last-child {
  margin-bottom: 0;
}

.error-item {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  flex: 1;
}

.review-time {
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  font-weight: 400;
  padding: 10px 17px;
}

/* 作文报告内容 */
.report-content {
  padding: 8px 18px 0 18px;
  background: #FFFFFF;
}

.report-main-area {
  width: 339px;
  height: 701px;
  background: #FFFFFF;
  border: 3px solid #BCC1CA;
  box-sizing: border-box;
  position: relative;
}

.report-bg-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 336px;
  height: 698px;
  pointer-events: none;
}

/* 作文要求内容 */
.requirements-content {
  background: #FFFFFF;
}

/* 标签信息区域 */
.requirements-header {
  width: 100%;
  height: 34px;
  background: #F8F9FA;
  display: flex;
  align-items: center;
  padding: 0 26px;
  gap: 12px;
  box-sizing: border-box;
}

.req-tag {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

/* 作文信息区域 */
.essay-info-section {
  padding: 12px 26px 0 26px;
}

.info-row {
  display: flex;
  margin-bottom: 26px;
}

.info-row.requirements-row {
  margin-bottom: 0;
}

.info-label {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  width: 44px;
  flex-shrink: 0;
}

.info-value {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
  margin-left: 12px;
}

.requirements-text {
  width: 263px;
  line-height: 18px;
}

/* 分隔区域 */
.requirements-separator {
  width: 100%;
  height: 20px;
  background: #F8F9FA;
  margin-top: 32px;
}

/* 上传作文区域 */
.upload-section {
  position: relative;
  padding: 0;
  margin-top: 0;
}

.upload-header-container {
  display: flex;
  align-items: center;
  padding-left: 24px;
  padding-top: 12px;
  margin-bottom: 12px;
  gap: 4px;
}

.upload-title {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.warning-icon {
  width: 16px;
  height: 16px;
}

.upload-grid {
  display: grid;
  grid-template-columns: 131px 131px;
  grid-template-rows: 76px 76px;
  gap: 20px 28px;
  padding: 0 43px;
  justify-content: space-between;
}

.upload-box {
  width: 131px;
  height: 76px;
  border: 3px solid #BCC1CA;
  background: #FFFFFF;
  cursor: pointer;
  transition: border-color 0.2s ease;
  position: relative;
  box-sizing: border-box;
}

.upload-box:hover {
  border-color: #636AE8;
}

.upload-icon {
  width: 128px;
  height: 73px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* 润色范文内容 */
.sample-content {
  background: #FFFFFF;
  padding-bottom: 20px;
}

.sample-title-section {
  width: 100%;
  height: 34px;
  background: #F8F9FA;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sample-title {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.sample-text-content {
  padding: 20px 28px;
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #323842;
}

.sample-text-content p {
  margin: 0 0 16px 0;
  text-indent: 2em;
}

.sample-text-content p:last-child {
  margin-bottom: 0;
}

.sample-buttons {
  display: flex;
  gap: 8px;
  padding: 0 10px;
  margin-top: 40px;
  justify-content: space-between;
}

.copy-button,
.download-button {
  width: 174px;
  height: 52px;
  background: #636AE8;
  border: none;
  border-radius: 16px;
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 2px 8px rgba(99, 106, 232, 0.3);
}

.copy-button:hover,
.download-button:hover {
  background: #5A61D9;
  transform: translateY(-1px);
  box-shadow: 0px 4px 12px rgba(99, 106, 232, 0.4);
}

.copy-button:active,
.download-button:active {
  background: #4F56CA;
  transform: translateY(0);
  box-shadow: 0px 2px 6px rgba(99, 106, 232, 0.3);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(23, 26, 31, 0.21);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
}

/* 弹窗头部 */
.modal-header {
  position: relative;
  padding: 0;
}

.modal-header-icons {
  width: 100%;
  height: 40px;
  background: transparent;
  border: 1px solid #BCC1CA;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  box-sizing: border-box;
}

.modal-header-icons .header-icon:first-child {
  width: 72px;
  height: 40px;
  border-right: 1px solid #BCC1CA;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('../assets/images/essay-detail-icon1.svg') no-repeat center;
  background-size: 27.34px 10.7px;
}

.modal-header-icons .header-icon:last-child {
  width: 96px;
  height: 40px;
  border-left: 1px solid #BCC1CA;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('../assets/images/essay-detail-icon2.svg') no-repeat center;
  background-size: 65.87px 10.56px;
}

.modal-back-button {
  position: absolute;
  left: 24px;
  top: 48px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.modal-back-button img {
  width: 100%;
  height: 100%;
}

.modal-title {
  position: absolute;
  left: 50%;
  top: 47px;
  transform: translateX(-50%);
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
}

.modal-description {
  position: absolute;
  left: 50%;
  top: 92px;
  transform: translateX(-50%);
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #9095A0;
}

.modal-subtitle {
  position: absolute;
  left: 50%;
  top: 121px;
  transform: translateX(-50%);
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #9095A0;
}

.modal-note {
  position: absolute;
  left: 50%;
  top: 150px;
  transform: translateX(-50%);
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #9095A0;
  width: 252px;
  text-align: center;
}

/* 弹窗内容区域 */
.modal-body {
  position: absolute;
  top: 170px;
  left: 0;
  width: 100%;
  height: 674px;
  background: #FFFFFF;
}

.essay-content-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 8px 18px 0 18px;
  box-sizing: border-box;
}

.essay-bg {
  width: 339px;
  height: 748px;
  border: 3px solid #BCC1CA;
  box-sizing: border-box;
}

/* 弹窗标签页 */
.modal-tabs {
  position: absolute;
  top: 170px;
  left: 98px;
  width: 180px;
  height: 40px;
  background: transparent;
}

.modal-tab-group {
  display: flex;
  width: 100%;
  height: 100%;
}

.modal-tab {
  width: 60px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  background: transparent;
}

.modal-tab span {
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: #565E6C;
  position: relative;
  z-index: 2;
}

.modal-tab.active span {
  font-weight: 700;
  color: #636AE8;
}

.tab-background {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: #636AE8;
  border: none;
}

/* 第二组标签页 */
.modal-tabs-second {
  position: absolute;
  top: 226px;
  left: 98px;
  width: 180px;
  height: 40px;
  background: transparent;
}

/* 数据回显说明 */
.modal-data-note {
  position: absolute;
  top: 281px;
  left: 50%;
  transform: translateX(-50%);
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #BCC1CA;
  width: 266px;
  text-align: center;
}
</style>
